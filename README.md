# Flutter Docker App

This project demonstrates how to dockerize a Flutter web application following the Codemagic tutorial approach.

## Features

- ✅ Complete Flutter environment in Docker
- ✅ No need to install Flutter locally
- ✅ Web application ready to run
- ✅ Docker Compose for easy management
- ✅ Optimized with .dockerignore

## Prerequisites

- Docker
- Docker Compose

## Quick Start

### Option 1: Using Docker Compose (Recommended)

```bash
# Build and run the application
docker-compose up --build

# Access the app at http://localhost:8080
```

### Option 2: Using Docker directly

```bash
# Build the Docker image
docker build -t flutter-docker-app .

# Run the container
docker run -p 8080:8080 flutter-docker-app

# Access the app at http://localhost:8080
```

## Development

### Hot Reload Development Mode

For development with hot reload, you can mount the source code:

```bash
docker-compose up
```

The docker-compose.yml is already configured with volume mounts for development.

### Building for Production

```bash
# Build production image
docker build -t flutter-docker-app:prod .

# Run production container
docker run -p 8080:8080 flutter-docker-app:prod
```

## Project Structure

```
.
├── Dockerfile              # Docker configuration
├── docker-compose.yml      # Docker Compose configuration
├── .dockerignore           # Files to ignore in Docker build
├── pubspec.yaml            # Flutter dependencies
├── lib/
│   └── main.dart           # Main Flutter application
├── web/
│   ├── index.html          # Web entry point
│   └── manifest.json       # Web app manifest
└── README.md               # This file
```

## Troubleshooting

### Port Already in Use
If port 8080 is already in use, change it in docker-compose.yml:
```yaml
ports:
  - "3000:8080"  # Use port 3000 instead
```

### Build Issues
If you encounter build issues, try:
```bash
# Clean up Docker
docker-compose down
docker system prune -f

# Rebuild
docker-compose up --build
```
