# Flutter Docker App

This project demonstrates how to dockerize a Flutter web application following the Codemagic tutorial approach.

## Features

- ✅ Complete Flutter environment in Docker
- ✅ No need to install Flutter locally
- ✅ Web application ready to run
- ✅ Docker Compose for easy management
- ✅ Optimized with .dockerignore

## Prerequisites

- Docker
- Docker Compose

## Quick Start

### Option 1: Development Mode (Recommended for development)

```bash
# Build and run in development mode with hot reload
docker compose up --build

# Access the app at http://localhost:8080
```

### Option 2: Production Mode (Lightweight Alpine + Nginx)

```bash
# Build and run production version
docker compose -f docker-compose.alpine.yml up --build

# Access the app at http://localhost:8080
```

### Option 3: Using Docker directly

```bash
# Development mode
docker build -t flutter-docker-app .
docker run -p 8080:8080 flutter-docker-app dev

# Production mode
docker run -p 8080:8080 flutter-docker-app prod

# Run tests
docker run flutter-docker-app test

# Analyze code
docker run flutter-docker-app analyze
```

## Development

### Hot Reload Development Mode

For development with hot reload, you can mount the source code:

```bash
docker-compose up
```

The docker-compose.yml is already configured with volume mounts for development.

### Building for Production

```bash
# Build production image
docker build -t flutter-docker-app:prod .

# Run production container
docker run -p 8080:8080 flutter-docker-app:prod
```

## Project Structure

```
.
├── Dockerfile                    # Development Docker configuration (Ubuntu-based)
├── Dockerfile.alpine             # Production Docker configuration (Alpine + Nginx)
├── docker-compose.yml            # Development Docker Compose
├── docker-compose.alpine.yml     # Production Docker Compose
├── docker-entrypoint.sh          # Entry script for different modes
├── nginx.conf                    # Nginx configuration for production
├── .dockerignore                 # Files to ignore in Docker build
├── pubspec.yaml                  # Flutter dependencies
├── lib/
│   └── main.dart                 # Main Flutter application
├── web/
│   ├── index.html                # Web entry point
│   └── manifest.json             # Web app manifest
└── README.md                     # This file
```

## Docker Configurations

### Development (Dockerfile)
- **Base**: Ubuntu 20.04
- **Flutter**: Downloaded from official releases
- **Features**: Hot reload, development tools
- **Size**: Larger but includes full development environment
- **Use case**: Development and testing

### Production (Dockerfile.alpine)
- **Base**: Alpine Linux + Nginx
- **Build**: Multi-stage build for smaller final image
- **Features**: Optimized for production serving
- **Size**: Much smaller final image
- **Use case**: Production deployment

## Troubleshooting

### Port Already in Use
If port 8080 is already in use, change it in docker-compose.yml:
```yaml
ports:
  - "3000:8080"  # Use port 3000 instead
```

### Build Issues
If you encounter build issues, try:
```bash
# Clean up Docker
docker-compose down
docker system prune -f

# Rebuild
docker-compose up --build
```
