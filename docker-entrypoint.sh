#!/bin/bash

set -e

# Function to run in development mode
run_dev() {
    echo "🚀 Starting Flutter in development mode..."
    flutter run --web-port 8080 --web-hostname 0.0.0.0 --hot
}

# Function to run in production mode
run_prod() {
    echo "🏗️  Building Flutter web app..."
    flutter build web --release
    
    echo "🌐 Starting simple HTTP server..."
    cd build/web
    python3 -m http.server 8080 --bind 0.0.0.0
}

# Function to run tests
run_test() {
    echo "🧪 Running Flutter tests..."
    flutter test
}

# Function to analyze code
run_analyze() {
    echo "🔍 Analyzing Flutter code..."
    flutter analyze
}

# Main logic
case "$1" in
    "dev")
        run_dev
        ;;
    "prod")
        run_prod
        ;;
    "test")
        run_test
        ;;
    "analyze")
        run_analyze
        ;;
    *)
        echo "Usage: $0 {dev|prod|test|analyze}"
        echo "  dev     - Run in development mode with hot reload"
        echo "  prod    - Build and serve production version"
        echo "  test    - Run tests"
        echo "  analyze - Analyze code"
        exit 1
        ;;
esac
