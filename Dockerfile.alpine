# Multi-stage build for smaller final image
FROM alpine:3.18 AS flutter-installer

# Install dependencies for Flutter
RUN apk add --no-cache \
    bash \
    curl \
    git \
    unzip \
    xz \
    libstdc++ \
    gcompat

# Install Flutter
ENV FLUTTER_HOME="/opt/flutter"
ENV PATH="$FLUTTER_HOME/bin:$PATH"

# Download Flutter
RUN curl -o flutter.tar.xz https://storage.googleapis.com/flutter_infra_release/releases/stable/linux/flutter_linux_3.16.9-stable.tar.xz \
    && tar -xf flutter.tar.xz -C /opt \
    && rm flutter.tar.xz

# Configure Flutter
RUN flutter config --enable-web \
    && flutter precache --web

# Build stage
FROM flutter-installer AS builder

WORKDIR /app

# Copy pubspec files first for better caching
COPY pubspec.* ./

# Get dependencies
RUN flutter pub get

# Copy source code
COPY . .

# Build web app
RUN flutter build web --release

# Runtime stage
FROM nginx:alpine

# Copy built web app to nginx
COPY --from=builder /app/build/web /usr/share/nginx/html

# Copy custom nginx config
COPY nginx.conf /etc/nginx/nginx.conf

EXPOSE 80

CMD ["nginx", "-g", "daemon off;"]
